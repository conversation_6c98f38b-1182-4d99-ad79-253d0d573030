"""
应用配置文件
"""

import os
from typing import List, Optional
from pydantic import BaseModel
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用设置"""
    
    # 基本设置
    PROJECT_NAME: str = "SmartCompass Backend"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # 安全设置
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7 days
    
    # CORS设置
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:8080",  # Vue.js开发服务器
        "http://localhost:3000",  # 备用前端端口
        "http://127.0.0.1:8080",
        "http://127.0.0.1:3000",
    ]
    
    # 数据库设置
    MYSQL_SERVER: str = os.getenv("MYSQL_SERVER", "db.iWell.com")
    MYSQL_PORT: int = int(os.getenv("MYSQL_PORT", "3306"))
    MYSQL_USER: str = os.getenv("MYSQL_USER", "root")
    MYSQL_PASSWORD: str = os.getenv("MYSQL_PASSWORD", "")
    MYSQL_DB: str = os.getenv("MYSQL_DB", "smart_compass")
    
    @property
    def SQLALCHEMY_DATABASE_URL(self) -> str:
        """构建数据库连接URL"""
        return f"mysql+pymysql://{self.MYSQL_USER}:{self.MYSQL_PASSWORD}@{self.MYSQL_SERVER}:{self.MYSQL_PORT}/{self.MYSQL_DB}"
    
    # Redis设置（如果需要缓存）
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://localhost:6379")
    
    # 邮件设置（如果需要发送邮件）
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # 其他设置
    USERS_OPEN_REGISTRATION: bool = True
    FIRST_SUPERUSER: str = os.getenv("FIRST_SUPERUSER", "<EMAIL>")
    FIRST_SUPERUSER_PASSWORD: str = os.getenv("FIRST_SUPERUSER_PASSWORD", "changethis")
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# 创建全局设置实例
settings = Settings() 