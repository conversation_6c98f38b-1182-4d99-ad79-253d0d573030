<template>
  <div class="home-page">
    <!-- 主要搜索区域 -->
    <section class="hero-section">
      <div class="container">
        <div class="hero-content">
          <h1 class="hero-title gradient-text">{{ $t('home.title') }}</h1>
          <p class="hero-subtitle">{{ $t('home.subtitle') }}</p>
          
          <!-- 搜索框 -->
          <div class="search-section">
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                :placeholder="$t('home.searchPlaceholder')"
                size="large"
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-button 
                type="primary" 
                size="large" 
                class="search-btn"
                @click="handleSearch"
                :loading="searching"
              >
                {{ $t('common.analyze') }}
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 内容区域 -->
    <section class="content-section">
      <div class="container">
        <div class="content-grid">
          <!-- 财经早报 -->
          <div class="news-section">
            <div class="section-header">
              <h2 class="section-title">
                <el-icon><Document /></el-icon>
                {{ $t('home.financialNews') }}
              </h2>
              <el-button text type="primary" size="small">
                {{ $t('home.more') }} <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
            
            <div class="news-list" v-loading="newsLoading">
              <div 
                v-for="news in newsList" 
                :key="news.id"
                class="news-item card"
              >
                <div class="news-content">
                  <h3 class="news-title">{{ news.title }}</h3>
                  <p class="news-summary">{{ news.summary }}</p>
                  <div class="news-meta">
                    <span class="news-source">{{ news.source }}</span>
                    <span class="news-time">{{ formatTime(news.publishTime) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 全球市场行情 -->
          <div class="market-section">
            <div class="section-header">
              <h2 class="section-title">
                <el-icon><TrendCharts /></el-icon>
                {{ $t('home.globalMarkets') }}
              </h2>
              <el-button text type="primary" size="small">
                {{ $t('home.more') }} <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>
            
            <div class="market-list" v-loading="marketLoading">
              <div 
                v-for="market in marketIndices" 
                :key="market.id"
                class="market-item card"
              >
                <div class="market-header">
                  <div class="market-info">
                    <h3 class="market-name">{{ market.name }}</h3>
                    <span class="market-code">{{ market.code }}</span>
                  </div>
                  <div class="market-value">
                    <span class="value">{{ market.value.toLocaleString() }}</span>
                    <span 
                      class="change"
                      :class="market.trend"
                    >
                      {{ market.change }}
                    </span>
                  </div>
                </div>
                
                <div class="market-analysis">
                  <p class="analysis-text">
                    <strong>{{ $t('home.yesterdayAnalysis') }}:</strong>
                    {{ market.analysis }}
                  </p>
                </div>
                
                <!-- 简单的趋势图 -->
                <div class="trend-chart">
                  <MiniChart :data="market.chartData" :trend="market.trend" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { ElMessage } from 'element-plus'
import { 
  Search, 
  Document, 
  TrendCharts, 
  ArrowRight 
} from '@element-plus/icons-vue'
import { getNews, getMarketIndices } from '@/api'
import MiniChart from '@/components/MiniChart.vue'

const router = useRouter()
const { t } = useI18n()

// 响应式数据
const searchQuery = ref('')
const searching = ref(false)
const newsLoading = ref(false)
const marketLoading = ref(false)
const newsList = ref([])
const marketIndices = ref([])

// 搜索股票
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning(t('home.searchWarning'))
    return
  }
  
  searching.value = true
  
  try {
    // 导航到股票分析页面
    await router.push(`/stock-analysis/${searchQuery.value.toUpperCase()}`)
  } catch (error) {
    ElMessage.error(t('home.searchError'))
  } finally {
    searching.value = false
  }
}

// 加载财经新闻
const loadNews = async () => {
  newsLoading.value = true
  try {
    newsList.value = await getNews()
  } catch (error) {
    ElMessage.error(t('home.loadNewsError'))
  } finally {
    newsLoading.value = false
  }
}

// 加载市场指数
const loadMarketIndices = async () => {
  marketLoading.value = true
  try {
    marketIndices.value = await getMarketIndices()
  } catch (error) {
    ElMessage.error(t('home.loadMarketError'))
  } finally {
    marketLoading.value = false
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const diff = now - date
  const hours = Math.floor(diff / (1000 * 60 * 60))
  
  if (hours < 1) {
    return t('home.justNow')
  } else if (hours < 24) {
    return t('home.hoursAgo', { hours })
  } else {
    return date.toLocaleDateString()
  }
}

// 组件挂载时加载数据
onMounted(() => {
  loadNews()
  loadMarketIndices()
})
</script>

<style lang="scss" scoped>
.home-page {
  min-height: 100vh;
}

.hero-section {
  background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
  padding: 80px 0 60px;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23f8fafc" fill-opacity="0.1" points="0,0 1000,300 1000,1000 0,700"/></svg>');
    background-size: cover;
  }
  
  .hero-content {
    text-align: center;
    position: relative;
    z-index: 1;
  }
  
  .hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 16px;
    
    @media (max-width: 768px) {
      font-size: 2.5rem;
    }
  }
  
  .hero-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    margin-bottom: 48px;
    
    @media (max-width: 768px) {
      font-size: 1.1rem;
      margin-bottom: 32px;
    }
  }
}

.search-section {
  max-width: 600px;
  margin: 0 auto;
  
  .search-box {
    display: flex;
    gap: 12px;
    align-items: center;
    
    @media (max-width: 480px) {
      flex-direction: column;
      gap: 16px;
    }
    
    .search-input {
      flex: 1;
      
      // 移除默认样式
      :deep(.el-input) {
        --el-input-bg-color: var(--bg-card);
        --el-input-border-color: var(--border-color);
        --el-input-hover-border-color: var(--primary-color);
        --el-input-focus-border-color: var(--primary-color);
      }
      
      :deep(.el-input__wrapper) {
        background-color: var(--bg-card) !important;
        border: 1px solid var(--border-color) !important;
        border-radius: 12px !important;
        box-shadow: none !important;
        transition: all 0.3s ease;
        
        &:hover {
          border-color: var(--primary-color) !important;
          box-shadow: 0 0 0 1px var(--primary-light) !important;
        }
        
        &.is-focus {
          border-color: var(--primary-color) !important;
          box-shadow: 0 0 0 2px var(--el-color-primary-light-7) !important;
        }
      }
      
      :deep(.el-input__inner) {
        background-color: var(--bg-card) !important;
        border: none !important;
        border-radius: 12px !important;
        font-size: 16px;
        color: var(--text-primary);
        box-shadow: none !important;
        
        &::placeholder {
          color: var(--text-light);
        }
        
        &:focus {
          background-color: var(--bg-card) !important;
          border: none !important;
          outline: none !important;
          box-shadow: none !important;
        }
      }
      
      // 确保前缀图标也使用正确的颜色
      :deep(.el-input__prefix) {
        color: var(--text-light);
      }
      
      :deep(.el-input__prefix-inner) {
        color: var(--text-light);
      }
    }
    
    .search-btn {
      height: 48px;
      padding: 0 32px;
      border-radius: 12px;
      font-weight: 600;
      
      @media (max-width: 480px) {
        width: 100%;
      }
    }
  }
}

.content-section {
  padding: 60px 0;
  background: var(--bg-primary);
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  
  @media (max-width: 1024px) {
    grid-template-columns: 1fr;
    gap: 32px;
  }
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  .section-title {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
  }
}

.news-section {
  .news-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .news-item {
    padding: 20px;
    cursor: pointer;
    
    &:hover {
      transform: translateY(-2px);
    }
    
    .news-title {
      font-size: 1.1rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: 8px;
      line-height: 1.4;
    }
    
    .news-summary {
      color: var(--text-secondary);
      font-size: 0.9rem;
      line-height: 1.5;
      margin-bottom: 12px;
    }
    
    .news-meta {
      display: flex;
      justify-content: space-between;
      align-items: center;
      font-size: 0.8rem;
      color: var(--text-light);
      
      .news-source {
        font-weight: 500;
      }
    }
  }
}

.market-section {
  .market-list {
    display: flex;
    flex-direction: column;
    gap: 16px;
  }
  
  .market-item {
    padding: 20px;
    
    .market-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 12px;
      
      .market-info {
        .market-name {
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--text-primary);
          margin-bottom: 4px;
        }
        
        .market-code {
          font-size: 0.85rem;
          color: var(--text-light);
        }
      }
      
      .market-value {
        text-align: right;
        
        .value {
          display: block;
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--text-primary);
        }
        
        .change {
          font-size: 0.9rem;
          font-weight: 600;
          
          &.up {
            color: var(--success-color);
          }
          
          &.down {
            color: var(--error-color);
          }
        }
      }
    }
    
    .market-analysis {
      margin-bottom: 16px;
      
      .analysis-text {
        font-size: 0.9rem;
        color: var(--text-secondary);
        line-height: 1.5;
      }
    }
    
    .trend-chart {
      height: 60px;
    }
  }
}
</style> 