<template>
  <div class="default-layout">
    <!-- 头部导航 -->
    <header class="header">
      <div class="container">
        <div class="header-content">
          <!-- Logo -->
          <div class="logo" @click="$router.push('/')">
            <div class="logo-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="32" height="32">
                <path fill="currentColor" d="M512 896a384 384 0 1 0 0-*********** 0 0 0 0 768m0 64a448 448 0 1 1 0-*********** 0 0 1 0 896"></path>
                <path fill="currentColor" d="M725.888 315.008C676.48 428.672 624 513.28 568.576 568.64c-55.424 55.424-139.968 107.904-253.568 157.312a12.8 12.8 0 0 1-16.896-16.832c49.536-113.728 102.016-198.272 157.312-253.632 55.36-55.296 139.904-107.776 253.632-157.312a12.8 12.8 0 0 1 16.832 16.832"></path>
              </svg>
            </div>
            <span class="logo-text">SmartCompass</span>
          </div>
          
          <!-- 桌面端导航 -->
          <nav class="nav-desktop">
            <router-link to="/" class="nav-link">{{ $t('nav.home') }}</router-link>
            <router-link to="/blog" class="nav-link">{{ $t('nav.blog') }}</router-link>
            <router-link to="/forum" class="nav-link">{{ $t('nav.forum') }}</router-link>
            <router-link to="/pricing" class="nav-link">{{ $t('nav.pricing') }}</router-link>
          </nav>
          
          <!-- 用户区域 -->
          <div class="user-area">
            <!-- 语言切换 -->
            <el-dropdown @command="changeLanguage" class="lang-dropdown">
              <el-button text>
                <el-icon><Setting /></el-icon>
                {{ currentLang }}
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="zh">中文</el-dropdown-item>
                  <el-dropdown-item command="en">English</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
            
            <!-- 主题切换 -->
            <el-button text @click="toggleTheme" class="theme-btn">
              <el-icon>
                <Sunny v-if="isDark" />
                <Moon v-else />
              </el-icon>
            </el-button>
            
            <!-- 用户信息 -->
            <div v-if="userStore.isLoggedIn" class="user-info">
              <el-dropdown @command="handleUserCommand">
                <div class="user-avatar">
                  <el-avatar :src="userStore.user.avatar" :size="32">
                    {{ userStore.user.name?.charAt(0) }}
                  </el-avatar>
                  <span class="user-name">{{ userStore.user.name }}</span>
                  <el-icon><ArrowDown /></el-icon>
                </div>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="profile">{{ $t('user.profile') }}</el-dropdown-item>
                    <el-dropdown-item command="my-page">{{ $t('user.myPage') }}</el-dropdown-item>
                    <el-dropdown-item divided command="logout">{{ $t('user.logout') }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
            
            <!-- 登录按钮 -->
            <div v-else class="auth-buttons">
              <el-button @click="$router.push('/auth')">{{ $t('auth.login') }}</el-button>
            </div>
            
            <!-- 移动端菜单按钮 -->
            <el-button 
              text 
              class="mobile-menu-btn"
              @click="mobileMenuVisible = true"
            >
              <el-icon size="24"><Menu /></el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </header>
    
    <!-- 主内容区域 -->
    <main class="main-content">
      <router-view />
    </main>
    
    <!-- 底部 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>{{ $t('footer.product') }}</h4>
            <ul>
              <li><router-link to="/pricing">{{ $t('nav.pricing') }}</router-link></li>
              <li><router-link to="/blog">{{ $t('nav.blog') }}</router-link></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ $t('footer.support') }}</h4>
            <ul>
              <li><a href="#">{{ $t('footer.help') }}</a></li>
              <li><a href="#">{{ $t('footer.contact') }}</a></li>
              <li><a href="#">{{ $t('footer.feedback') }}</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>{{ $t('footer.company') }}</h4>
            <ul>
              <li><a href="#">{{ $t('footer.about') }}</a></li>
              <li><a href="#">{{ $t('footer.careers') }}</a></li>
              <li><a href="#">{{ $t('footer.privacy') }}</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 SmartCompass. {{ $t('footer.rights') }}</p>
        </div>
      </div>
    </footer>
    
    <!-- 移动端菜单 -->
    <el-drawer
      v-model="mobileMenuVisible"
      direction="rtl"
      size="280px"
      class="mobile-menu"
    >
      <template #header>
        <div class="mobile-menu-header">
          <div class="logo">
            <div class="logo-icon">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024" width="24" height="24">
                <path fill="currentColor" d="M512 896a384 384 0 1 0 0-*********** 0 0 0 0 768m0 64a448 448 0 1 1 0-*********** 0 0 1 0 896"></path>
                <path fill="currentColor" d="M725.888 315.008C676.48 428.672 624 513.28 568.576 568.64c-55.424 55.424-139.968 107.904-253.568 157.312a12.8 12.8 0 0 1-16.896-16.832c49.536-113.728 102.016-198.272 157.312-253.632 55.36-55.296 139.904-107.776 253.632-157.312a12.8 12.8 0 0 1 16.832 16.832"></path>
              </svg>
            </div>
            <span class="logo-text">SmartCompass</span>
          </div>
        </div>
      </template>
      
      <div class="mobile-menu-content">
        <nav class="mobile-nav">
          <router-link to="/" @click="mobileMenuVisible = false">{{ $t('nav.home') }}</router-link>
          <router-link to="/blog" @click="mobileMenuVisible = false">{{ $t('nav.blog') }}</router-link>
          <router-link to="/forum" @click="mobileMenuVisible = false">{{ $t('nav.forum') }}</router-link>
          <router-link to="/pricing" @click="mobileMenuVisible = false">{{ $t('nav.pricing') }}</router-link>
        </nav>
        
        <div class="mobile-user-section" v-if="userStore.isLoggedIn">
          <div class="user-info">
            <el-avatar :src="userStore.user.avatar" :size="40">
              {{ userStore.user.name?.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ userStore.user.name }}</span>
          </div>
          <div class="user-actions">
            <el-button @click="$router.push('/my-page'); mobileMenuVisible = false">
              {{ $t('user.myPage') }}
            </el-button>
            <el-button @click="handleLogout">{{ $t('user.logout') }}</el-button>
          </div>
        </div>
        
        <div class="mobile-auth-section" v-else>
          <el-button type="primary" @click="$router.push('/auth'); mobileMenuVisible = false">
            {{ $t('auth.login') }}
          </el-button>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useUserStore } from '@/store/user'
import { 
  Setting, Sunny, Moon, Menu, ArrowDown 
} from '@element-plus/icons-vue'

export default defineComponent({
  name: 'DefaultLayout',
  components: {
    Setting, Sunny, Moon, Menu, ArrowDown
  },
  setup() {
    const router = useRouter()
    const { locale } = useI18n()
    const userStore = useUserStore()
    
    const mobileMenuVisible = ref(false)
    const isDark = ref(true)
    
    const currentLang = computed(() => {
      return locale.value === 'zh' ? '中文' : 'English'
    })
    
    const changeLanguage = (lang) => {
      locale.value = lang
      localStorage.setItem('language', lang)
    }
    
    const toggleTheme = () => {
      isDark.value = !isDark.value
      const theme = isDark.value ? 'dark' : 'light'
      
      // 设置Element Plus暗色主题
      document.documentElement.classList.toggle('dark', isDark.value)
      
      // 设置自定义主题属性
      document.documentElement.setAttribute('data-theme', theme)
      
      // 动态切换favicon
      updateFavicon(theme)
      
      // 保存主题设置
      localStorage.setItem('theme', theme)
    }
    
    // 更新favicon
    const updateFavicon = (theme) => {
      const svgIcon = document.querySelector('link[type="image/svg+xml"]')
      const shortcutIcon = document.querySelector('link[rel="shortcut icon"]')
      
      if (theme === 'dark') {
        if (svgIcon) svgIcon.href = '/logo-dark.svg'
        if (shortcutIcon) shortcutIcon.href = '/logo-dark.svg'
      } else {
        if (svgIcon) svgIcon.href = '/logo.svg'
        if (shortcutIcon) shortcutIcon.href = '/logo.svg'
      }
    }
    
    const handleUserCommand = (command) => {
      switch (command) {
        case 'profile':
          router.push('/my-page')
          break
        case 'my-page':
          router.push('/my-page')
          break
        case 'logout':
          handleLogout()
          break
      }
    }
    
    const handleLogout = () => {
      userStore.logout()
      router.push('/')
      mobileMenuVisible.value = false
    }
    
    // 初始化语言 (默认英文)
    const savedLanguage = localStorage.getItem('language')
    if (!savedLanguage) {
      locale.value = 'en'
      localStorage.setItem('language', 'en')
    }
    
    // 初始化主题 (默认暗色主题)
    const savedTheme = localStorage.getItem('theme')
    if (savedTheme === 'light') {
      isDark.value = false
      document.documentElement.classList.remove('dark')
      document.documentElement.setAttribute('data-theme', 'light')
      updateFavicon('light')
    } else {
      isDark.value = true
      document.documentElement.classList.add('dark')
      document.documentElement.setAttribute('data-theme', 'dark')
      updateFavicon('dark')
    }
    
    return {
      userStore,
      mobileMenuVisible,
      isDark,
      currentLang,
      changeLanguage,
      toggleTheme,
      handleUserCommand,
      handleLogout
    }
  }
})
</script>

<style lang="scss" scoped>
.default-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--el-border-color-light);
  position: sticky;
  top: 0;
  z-index: 1000;
  
  [data-theme="dark"] & {
    background: rgba(15, 23, 42, 0.95);
    border-bottom-color: var(--border-color);
  }
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 64px;
  }
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  
  .logo-icon {
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .logo-text {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--el-text-color-primary);
  }
}

.nav-desktop {
  display: flex;
  gap: 32px;
  
  .nav-link {
    color: var(--el-text-color-regular);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s;
    
    &:hover,
    &.router-link-active {
      color: var(--el-color-primary);
    }
  }
  
  @media (max-width: 768px) {
    display: none;
  }
}

.user-area {
  display: flex;
  align-items: center;
  gap: 16px;
}

.lang-dropdown,
.theme-btn {
  @media (max-width: 768px) {
    display: none;
  }
}

.user-info {
  .user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    
    .user-name {
      font-weight: 500;
      color: var(--el-text-color-primary);
    }
  }
}

.auth-buttons {
  @media (max-width: 768px) {
    display: none;
  }
}

.mobile-menu-btn {
  display: none;
  
  @media (max-width: 768px) {
    display: flex;
  }
}

.main-content {
  flex: 1;
}

.footer {
  background: var(--el-bg-color-page);
  border-top: 1px solid var(--el-border-color-light);
  padding: 40px 0 20px;
  
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
  }
  
  .footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 32px;
    margin-bottom: 32px;
  }
  
  .footer-section {
    h4 {
      font-weight: 600;
      margin-bottom: 16px;
      color: var(--el-text-color-primary);
    }
    
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
      
      li {
        margin-bottom: 8px;
        
        a {
          color: var(--el-text-color-regular);
          text-decoration: none;
          transition: color 0.3s;
          
          &:hover {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
  
  .footer-bottom {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid var(--el-border-color-light);
    
    p {
      color: var(--el-text-color-secondary);
      margin: 0;
    }
  }
}

// 移动端菜单样式
:deep(.mobile-menu) {
  .el-drawer__header {
    margin-bottom: 0;
    padding: 20px;
    border-bottom: 1px solid var(--el-border-color-light);
  }
  
  .el-drawer__body {
    padding: 0;
  }
}

.mobile-menu-header {
  .logo {
    .logo-text {
      font-size: 1.25rem;
    }
  }
}

.mobile-menu-content {
  padding: 20px;
}

.mobile-nav {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 32px;
  
  a {
    color: var(--el-text-color-primary);
    text-decoration: none;
    font-weight: 500;
    padding: 12px 0;
    border-bottom: 1px solid var(--el-border-color-lighter);
    
    &.router-link-active {
      color: var(--el-color-primary);
    }
  }
}

.mobile-user-section {
  .user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .user-name {
      font-weight: 500;
    }
  }
  
  .user-actions {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .el-button {
      width: 100%;
    }
  }
}

.mobile-auth-section {
  .el-button {
    width: 100%;
  }
}
</style> 